# E2E Testing Tips & Best Practices

> **💡 Hard-earned wisdom from building a production-ready E2E test suite**

This guide contains all the tips, tricks, and lessons learned while setting up our DRY, fast, and reliable E2E testing system with Playwright.

## 🚀 Quick Start

```bash
# Super fast tests (3-5x faster than default)
pnpm test:e2e:fast

# Basic functionality tests (~4 seconds)
pnpm test:e2e:fast tests/basic-app-functionality.spec.ts

# Activity management tests (requires admin user)
pnpm test:e2e:fast tests/client-activity-management.spec.ts

# Debug mode when things go wrong
pnpm test:e2e:debug tests/debug-admin-page.spec.ts
```

## 🎯 Core Principles

### 1. **DRY (Don't Repeat Yourself) is KING**

- ✅ **Create reusable helpers** for common actions
- ✅ **Centralize test data** in utility modules
- ✅ **Share authentication** across all tests
- ❌ **Never copy-paste test code** - refactor into helpers

### 2. **Speed is Everything**

- ✅ **Aggressive timeouts** (15s test, 5s action, 3s assertion)
- ✅ **Single browser** for development (Chromium only)
- ✅ **Parallel execution** with multiple workers
- ✅ **Global authentication** setup once, use everywhere
- ❌ **Don't wait for `networkidle`** - use `domcontentloaded`

### 3. **Fail Fast, Debug Faster**

- ✅ **Capture errors early** with console/page error listeners
- ✅ **Take screenshots** on failure
- ✅ **Use traces** for complex debugging
- ✅ **Clear error messages** that tell you exactly what's wrong

## 🔧 Essential Setup

### Authentication Strategy

```typescript
// ✅ GOOD: Global setup with fallback
// tests/global.setup.ts
async function globalSetup(config: FullConfig) {
  // Authenticate once, save state, reuse everywhere
  await page.context().storageState({ path: STORAGE_STATE });
}

// ✅ GOOD: DRY auth helpers
// tests/utils/auth.ts
export async function loginAs(page: Page, options: LoginOptions) {
  // Validate auth state, provide clear error messages
}
```

### Test Data Management

```typescript
// ✅ GOOD: Reusable test data
export const TEST_ACTIVITIES = {
  BASIC: { name: 'Test Basic Activity', points: 10, icon: 'activity' },
  HIGH_VALUE: { name: 'Test High Value Activity', points: 100, icon: 'star' },
} as const;

// ✅ GOOD: Cleanup helpers
export async function cleanupTestActivities(page: Page) {
  // Clean up only test data (prefixed with "Test")
}
```

## ⚡ Speed Optimizations

### Playwright Config

```typescript
export default defineConfig({
  // 🚀 FAST: Aggressive timeouts
  timeout: 15 * 1000,
  expect: { timeout: 3 * 1000 },

  use: {
    actionTimeout: 5 * 1000,
    navigationTimeout: 10 * 1000,
    video: 'off', // 🚀 Disable for speed
  },

  // 🚀 FAST: Single browser for dev
  projects: [{ name: 'chromium', use: { ...devices['Desktop Chrome'] } }],
});
```

### Test Code Optimizations

```typescript
// ✅ FAST: Reduced waits
await page.waitForTimeout(200); // Not 1000ms

// ✅ FAST: Specific selectors
await page.getByRole('button', { name: 'Create', exact: true });

// ✅ FAST: DOM ready instead of network idle
await page.waitForLoadState('domcontentloaded');
```

## 🐛 Debugging Strategies

### 1. **Start with Screenshots**

```typescript
// Always take screenshots when debugging
await page.screenshot({ path: 'debug-issue.png', fullPage: true });
```

### 2. **Capture JavaScript Errors**

```typescript
const consoleErrors: string[] = [];
page.on('console', (msg) => {
  if (msg.type() === 'error') consoleErrors.push(msg.text());
});

const pageErrors: string[] = [];
page.on('pageerror', (error) => pageErrors.push(error.message));
```

### 3. **Use Traces for Complex Issues**

```bash
# Generate trace
pnpm test:e2e:debug tests/failing-test.spec.ts

# View trace
pnpm exec playwright show-trace test-results/.../trace.zip
```

## 🎭 Common Pitfalls & Solutions

### Authentication Issues

```typescript
// ❌ WRONG: Assuming auth works
await page.goto('/admin');
await page.getByRole('tab', { name: 'Activities' }).click();

// ✅ RIGHT: Validate auth state
await loginAs(page, { email: '<EMAIL>', role: 'admin' });
await requireAdminAccess(page); // Throws clear error if no access
await navigateToActivitiesTab(page);
```

### Flaky Selectors

```typescript
// ❌ WRONG: Generic selectors
await page.click('button');

// ✅ RIGHT: Specific, accessible selectors
await page
  .getByRole('button', { name: 'Create Activity', exact: true })
  .click();
```

### Timing Issues

```typescript
// ❌ WRONG: Fixed waits
await page.waitForTimeout(5000);

// ✅ RIGHT: Wait for specific conditions
await expect(page.getByText('Success!')).toBeVisible({ timeout: 3000 });
```

### Environment Differences

```typescript
// ❌ WRONG: Hardcoded values
await page.goto('http://localhost:3000/admin');

// ✅ RIGHT: Use baseURL from config
await page.goto('/admin'); // Uses baseURL from playwright.config.ts
```

## 📁 File Organization

```
tests/
├── global.setup.ts              # Global auth setup
├── utils/
│   ├── auth.ts                  # DRY auth helpers
│   ├── test-data.ts             # DRY test data helpers
│   └── navigation.ts            # DRY navigation helpers
├── basic-app-functionality.spec.ts    # Fast basic tests
├── client-activity-management.spec.ts # Feature-specific tests
├── admin-rewards-management.spec.ts   # Admin rewards tests
├── admin-milestone-management.spec.ts # Admin milestone tests
├── user-rewards-redemption.spec.ts    # User redemption flow
└── debug-*.spec.ts              # Debug helpers
```

## 🔍 Test Categories

### 1. **Basic Functionality Tests** (Always Fast)

- Landing page loads
- Navigation works
- Authentication redirects
- No JavaScript errors

### 2. **Feature Tests** (Require Setup)

- CRUD operations
- User workflows
- Admin functions
- Data validation

### 3. **Debug Tests** (Temporary)

- Capture page state
- Log errors
- Test specific issues
- Remove after fixing

## 🎯 Best Practices

### Test Structure

```typescript
test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    // DRY setup using helpers
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await navigateToFeature(page);
  });

  test.afterEach(async ({ page }) => {
    // DRY cleanup
    await cleanupTestData(page);
  });

  test('should do specific thing', async ({ page }) => {
    // Use DRY helpers for actions
    await createTestItem(page, TEST_DATA.BASIC);

    // Clear assertions
    await expect(page.getByText('Success!')).toBeVisible();
  });
});
```

### Error Messages

```typescript
// ✅ GOOD: Clear, actionable error messages
if (page.url().includes('sign-in')) {
  throw new Error(
    `Authentication required for test. Please ensure user ${email} exists in Clerk and has ${role} role.`
  );
}
```

### Constants & Validation

```typescript
// ✅ GOOD: Reusable validation patterns
export const VALIDATION_MESSAGES = {
  NAME_TOO_SHORT: 'Name must be at least 3 characters long',
  POINTS_POSITIVE: 'Points must be a positive number',
} as const;
```

## 🚨 Troubleshooting Guide

### "Test times out"

1. Check if dev server is running
2. Verify authentication is working
3. Look for JavaScript errors
4. Check network requests in trace

### "Element not found"

1. Take screenshot to see actual page
2. Check if authentication failed
3. Verify element exists with correct role/text
4. Use more specific selectors

### "Authentication failed"

1. Check user exists in Clerk dashboard
2. Verify user has correct role in database
3. Check environment variables
4. Test manual login flow

### "Tests are slow"

1. Use `test:e2e:fast` command
2. Disable video recording
3. Use single browser
4. Reduce timeout values
5. Avoid `networkidle` waits

## 🎉 Success Metrics

- ✅ **Tests run in under 30 seconds** for full suite
- ✅ **No flaky tests** - consistent pass/fail
- ✅ **Clear error messages** when tests fail
- ✅ **Easy to add new tests** using DRY helpers
- ✅ **Fast feedback loop** for developers

## 🎬 Real-World Examples

### Creating a New Test Suite

```typescript
// 1. Start with basic structure
test.describe('NEW-FEATURE: Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await navigateToFeature(page);
  });

  // 2. Add one simple test first
  test('should display feature interface', async ({ page }) => {
    await expect(page.getByText('Feature Title')).toBeVisible();
  });
});

// 3. Run it: pnpm test:e2e:fast tests/new-feature.spec.ts
// 4. Add more tests using DRY helpers
```

### Debugging a Failing Test

```bash
# 1. Run in debug mode
pnpm test:e2e:debug tests/failing-test.spec.ts

# 2. Check screenshots
open test-results/*/test-failed-*.png

# 3. View trace for detailed timeline
pnpm exec playwright show-trace test-results/*/trace.zip

# 4. Add console error logging to test
# 5. Fix issue and re-run with fast command
```

## 🔄 Development Workflow

### Daily Development

```bash
# 1. Start dev servers (use VSCode terminal sessions)
pnpm dev

# 2. Run fast tests while developing
pnpm test:e2e:fast tests/basic-*

# 3. Test specific feature you're working on
pnpm test:e2e:fast tests/your-feature.spec.ts

# 4. Before committing, run full suite
pnpm test:e2e
```

### Adding New Features

1. **Write basic test first** (TDD approach)
2. **Use DRY helpers** from day one
3. **Test authentication requirements** early
4. **Add cleanup** in afterEach
5. **Run fast tests** frequently

### Debugging Issues

1. **Start with screenshots** - see what's actually happening
2. **Check console errors** - JavaScript issues are common
3. **Verify authentication** - most failures are auth-related
4. **Use traces** for complex timing issues
5. **Create debug test** for persistent issues

## 📊 Performance Benchmarks

### Before Optimization

- ⏱️ **Test timeout**: Unlimited (often 30+ seconds per test)
- ⏱️ **Action timeout**: 30 seconds
- ⏱️ **Full suite**: 5-10 minutes
- 🌐 **Browsers**: Chrome, Firefox, Safari
- 📹 **Video**: Always recorded

### After Optimization

- ⚡ **Test timeout**: 15 seconds
- ⚡ **Action timeout**: 5 seconds
- ⚡ **Full suite**: 1-2 minutes
- 🌐 **Browsers**: Chrome only (dev), all (CI)
- 📹 **Video**: Disabled for speed

### Speed Comparison

```bash
# SLOW (old way)
pnpm test:e2e  # ~5-10 minutes

# FAST (new way)
pnpm test:e2e:fast  # ~1-2 minutes (3-5x faster!)
```

## 🎯 Team Guidelines

### Code Review Checklist

- [ ] Uses DRY helpers instead of duplicated code
- [ ] Has proper cleanup in afterEach
- [ ] Uses specific, accessible selectors
- [ ] Includes clear error messages
- [ ] Runs fast (under 15 seconds per test)

### When to Write E2E Tests

- ✅ **Critical user journeys** (login, purchase, etc.)
- ✅ **Cross-component interactions**
- ✅ **Admin workflows**
- ✅ **Authentication flows**
- ❌ **Unit test scenarios** (use Jest instead)
- ❌ **Simple component behavior** (use React Testing Library)

### Naming Conventions

```typescript
// ✅ GOOD: Descriptive test names
test('should create activity and show success message');
test('should validate required fields and show errors');
test('should redirect unauthenticated users to sign-in');

// ❌ BAD: Vague test names
test('test activity creation');
test('validation test');
test('auth test');
```

## 📚 Resources

- [Playwright Best Practices](https://playwright.dev/docs/best-practices)
- [Our DRY Helpers](../tests/utils/) - Reusable test utilities
- [VSCode Sessions](../.vscode/sessions.json) - Pre-configured terminals
- [Debug Commands](../tests/debug-*.spec.ts) - When things go wrong
- [Global Setup](../tests/global.setup.ts) - Authentication setup
- [Test Data Helpers](../tests/utils/test-data.ts) - DRY test data management

## 🆘 Getting Help

### Common Commands

```bash
# View all available test commands
pnpm run | grep test

# Get Playwright help
pnpm exec playwright --help

# View test results in browser
pnpm exec playwright show-report

# Install Playwright browsers
pnpm exec playwright install
```

### When Tests Fail

1. **Don't panic** - tests fail for good reasons
2. **Read the error message** - it usually tells you exactly what's wrong
3. **Check screenshots** - see what the page actually looks like
4. **Verify authentication** - most issues are auth-related
5. **Ask for help** - share the error message and screenshot

---

> **💡 Remember**: The goal is fast, reliable, maintainable tests that give you confidence to ship. Speed and DRY principles are your best friends!
>
> **🚀 Pro Tip**: Use `pnpm test:e2e:fast` for daily development - it's 3-5x faster and gives you instant feedback!
