import { Page, expect } from '@playwright/test';

/**
 * DRY helpers for managing test data across all E2E tests
 */

export interface ActivityTypeData {
  name: string;
  points: number;
  icon: string;
  key?: string;
}

export interface RewardData {
  name: string;
  description: string;
  cost: number;
  icon: string;
}

export interface MilestoneData {
  name: string;
  description: string;
  activityMatcher: string; // The KEY of the activity type
  countThreshold: number;
  pointReward: number;
}

/**
 * Create a test activity type (DRY helper)
 */
export async function createTestActivity(
  page: Page,
  data: ActivityTypeData
): Promise<void> {
  console.log(`📝 Creating test activity: ${data.name}`);

  await page.getByRole('button', { name: 'Create Activity' }).click();

  // Fill out the form
  await page.getByLabel('Name').fill(data.name);
  await page.getByLabel('Points').fill(data.points.toString());
  await page.getByLabel('Icon Name').fill(data.icon);

  // Submit the form
  await page.getByRole('button', { name: 'Create' }).click();

  // Verify success
  await expect(
    page.getByText('Activity type successfully created!')
  ).toBeVisible();

  console.log(`✅ Created activity: ${data.name}`);
}

/**
 * Create a test reward (DRY helper)
 */
export async function createTestReward(
  page: Page,
  data: RewardData
): Promise<void> {
  console.log(`📝 Creating test reward: ${data.name}`);

  await page.getByRole('button', { name: 'Create Reward' }).click();

  // Fill out the form
  await page.getByLabel('Name').fill(data.name);
  await page.getByLabel('Description').fill(data.description);
  await page.getByLabel('Point Cost').fill(data.cost.toString());
  await page.getByLabel('Image URL').fill(data.icon);

  // Submit the form
  await page.getByRole('button', { name: 'Save' }).click();

  // Verify success
  await expect(page.getByText('Reward successfully created!')).toBeVisible();

  console.log(`✅ Created reward: ${data.name}`);
}

/**
 * Create a test milestone (DRY helper)
 */
export async function createTestMilestone(
  page: Page,
  data: MilestoneData
): Promise<void> {
  console.log(`📝 Creating test milestone: ${data.name}`);

  await page.getByRole('button', { name: 'Create Milestone' }).click();

  // Fill out the form
  await page.getByLabel('Name').fill(data.name);
  await page.getByLabel('Description').fill(data.description);
  await page
    .getByLabel('Activity Type')
    .selectOption({ label: data.activityMatcher });
  await page.getByLabel('Count Threshold').fill(data.countThreshold.toString());
  await page.getByLabel('Point Reward').fill(data.pointReward.toString());

  // Submit the form
  await page.getByRole('button', { name: 'Save' }).click();

  // Verify success
  await expect(page.getByText('Milestone successfully created!')).toBeVisible();

  console.log(`✅ Created milestone: ${data.name}`);
}

/**
 * Delete a test activity type by name (DRY helper)
 */
export async function deleteTestActivity(
  page: Page,
  activityName: string
): Promise<void> {
  console.log(`🗑️ Deleting test activity: ${activityName}`);

  // Find the row and click delete
  const row = page.getByRole('row', { name: new RegExp(activityName) });
  await row.getByRole('button', { name: 'Delete' }).click();

  // Verify deletion
  await expect(
    page.getByText('Activity type deleted successfully!')
  ).toBeVisible();

  console.log(`✅ Deleted activity: ${activityName}`);
}

/**
 * Delete a test reward by name (DRY helper)
 */
export async function deleteTestReward(
  page: Page,
  rewardName: string
): Promise<void> {
  console.log(`🗑️ Deleting test reward: ${rewardName}`);

  // Find the row and click delete
  const row = page.getByRole('row', { name: new RegExp(rewardName) });
  await row.getByRole('button', { name: 'Delete' }).click();

  // Confirm deletion
  await page.getByRole('button', { name: 'Delete' }).click();

  // Verify deletion
  await expect(page.getByText('Reward deleted successfully!')).toBeVisible();

  console.log(`✅ Deleted reward: ${rewardName}`);
}

/**
 * Delete a test milestone by name (DRY helper)
 */
export async function deleteTestMilestone(
  page: Page,
  milestoneName: string
): Promise<void> {
  console.log(`🗑️ Deleting test milestone: ${milestoneName}`);

  const row = page.getByRole('row', { name: new RegExp(milestoneName) });
  await row.getByRole('button', { name: 'Delete' }).click();

  await page.getByRole('button', { name: 'Delete' }).click();

  await expect(page.getByText('Milestone successfully deleted!')).toBeVisible();

  console.log(`✅ Deleted milestone: ${milestoneName}`);
}

/**
 * Check if an activity exists in the table
 */
export async function activityExists(
  page: Page,
  activityName: string
): Promise<boolean> {
  try {
    await page.getByRole('cell', { name: activityName }).waitFor({
      state: 'visible',
      timeout: 2000,
    });
    return true;
  } catch {
    return false;
  }
}

/**
 * Seed test activities for tests that need existing data
 */
export async function seedTestActivities(page: Page): Promise<void> {
  console.log('🌱 Seeding test activities...');

  const testActivities: ActivityTypeData[] = [
    { name: 'Test Personal Training', points: 50, icon: 'dumbbell' },
    { name: 'Test Group Fitness', points: 20, icon: 'users' },
    { name: 'Test Yoga Class', points: 15, icon: 'yoga' },
  ];

  for (const activity of testActivities) {
    // Only create if it doesn't already exist
    if (!(await activityExists(page, activity.name))) {
      await createTestActivity(page, activity);
    } else {
      console.log(`⏭️ Activity ${activity.name} already exists, skipping`);
    }
  }

  console.log('✅ Test activities seeded');
}

/**
 * Check if a reward exists in the table
 */
export async function rewardExists(
  page: Page,
  rewardName: string
): Promise<boolean> {
  try {
    await page.getByRole('cell', { name: rewardName }).waitFor({
      state: 'visible',
      timeout: 2000,
    });
    return true;
  } catch {
    return false;
  }
}

/**
 * Seed test rewards for tests that need existing data
 */
export async function seedTestRewards(page: Page): Promise<void> {
  console.log('🌱 Seeding test rewards...');

  const testRewards: RewardData[] = [
    TEST_REWARDS.BASIC,
    TEST_REWARDS.HIGH_COST,
  ];

  for (const reward of testRewards) {
    if (!(await rewardExists(page, reward.name))) {
      await createTestReward(page, reward);
    } else {
      console.log(`⏭️ Reward ${reward.name} already exists, skipping`);
    }
  }

  console.log('✅ Test rewards seeded');
}

/**
 * Clean up all test activities (those starting with "Test")
 */
export async function cleanupTestActivities(page: Page): Promise<void> {
  console.log('🧹 Cleaning up test activities...');

  try {
    // Get all rows in the activities table
    const rows = page.getByRole('row');
    const rowCount = await rows.count();

    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const rowText = await row.textContent();

      // If this row contains a test activity (starts with "Test"), delete it
      if (rowText && rowText.includes('Test ') && rowText.includes('Delete')) {
        try {
          await row.getByRole('button', { name: 'Delete' }).click();
          await expect(
            page.getByText('Activity type deleted successfully!')
          ).toBeVisible();
          console.log(
            `🗑️ Deleted test activity from row: ${rowText.substring(0, 50)}...`
          );

          // Wait a bit for the UI to update
          await page.waitForTimeout(500);
        } catch (error) {
          console.log(`⚠️ Could not delete activity from row: ${error}`);
        }
      }
    }
  } catch (error) {
    console.log('⚠️ Cleanup encountered an error:', error);
  }

  console.log('✅ Test activity cleanup complete');
}

/**
 * Wait for activity table to load
 */
export async function waitForActivityTable(page: Page): Promise<void> {
  await expect(page.getByRole('table')).toBeVisible();
  await expect(page.getByRole('columnheader', { name: 'Name' })).toBeVisible();
  console.log('✅ Activity table loaded');
}

/**
 * Wait for reward table to load
 */
export async function waitForRewardTable(page: Page): Promise<void> {
  await expect(page.getByRole('table')).toBeVisible();
  await expect(page.getByRole('columnheader', { name: 'Name' })).toBeVisible();
  console.log('✅ Reward table loaded');
}

/**
 * Wait for milestone table to load
 */
export async function waitForMilestoneTable(page: Page): Promise<void> {
  await expect(page.getByRole('table')).toBeVisible();
  await expect(page.getByRole('columnheader', { name: 'Name' })).toBeVisible();
  console.log('✅ Milestone table loaded');
}

/**
 * Get activity data from table row
 */
export async function getActivityFromTable(
  page: Page,
  activityName: string
): Promise<{ name: string; points: string; icon: string } | null> {
  try {
    const row = page.getByRole('row', { name: new RegExp(activityName) });
    await row.waitFor({ state: 'visible', timeout: 2000 });

    const cells = row.getByRole('cell');
    const name = await cells.nth(0).textContent();
    const points = await cells.nth(2).textContent(); // Assuming points is 3rd column
    const icon = await cells.nth(3).textContent(); // Assuming icon is 4th column

    return {
      name: name || '',
      points: points || '',
      icon: icon || '',
    };
  } catch {
    return null;
  }
}

/**
 * Common test data sets for reuse
 */
export const TEST_ACTIVITIES = {
  BASIC: { name: 'Test Basic Activity', points: 10, icon: 'activity' },
  HIGH_VALUE: { name: 'Test High Value Activity', points: 100, icon: 'star' },
  LOW_VALUE: { name: 'Test Low Value Activity', points: 5, icon: 'circle' },
  SPECIAL_CHARS: {
    name: 'Test Special-Activity_123',
    points: 25,
    icon: 'special',
  },
} as const;

/**
 * Common test rewards for reuse
 */
export const TEST_REWARDS = {
  BASIC: {
    name: 'Test Branded Water Bottle',
    description: 'Stay hydrated in style.',
    cost: 350,
    icon: 'bottle',
  },
  HIGH_COST: {
    name: 'Test Personal Training Session',
    description: 'A one-hour session with a top trainer.',
    cost: 2500,
    icon: 'activity',
  },
} as const;

/**
 * Common test milestones for reuse
 */
export const TEST_MILESTONES = {
  TEN_CLASSES: {
    name: 'Test Milestone: 10 Classes',
    description: 'Attend 10 classes of any type.',
    activityMatcher: 'Class Attendance', // This should match the NAME of an activity type
    countThreshold: 10,
    pointReward: 100,
  },
  FIVE_YOGA: {
    name: 'Test Milestone: 5 Yoga Sessions',
    description: 'Find your zen in 5 yoga classes.',
    activityMatcher: 'Test Yoga Class', // This requires a "Test Yoga Class" activity type to exist
    countThreshold: 5,
    pointReward: 50,
  },
} as const;

/**
 * Common validation patterns
 */
export const VALIDATION_MESSAGES = {
  NAME_TOO_SHORT: 'Name must be at least 3 characters long',
  KEY_TOO_SHORT: 'Key must be at least 3 characters long',
  ICON_REQUIRED: 'Icon name cannot be empty',
  POINTS_POSITIVE: 'Points must be a positive number',
  COST_POSITIVE: 'Cost must be a positive number',
  DUPLICATE_KEY: /Activity type with key.*already exists/i,
  CANNOT_DELETE: /cannot be deleted.*used by milestones/i,
} as const;
