import { Page, expect } from '@playwright/test';

interface LoginOptions {
  email: string;
  role: 'admin' | 'staff' | 'member';
}

/**
 * Authentication helper for E2E tests
 * Since global setup handles authentication, this function mainly validates auth state
 */
export async function loginAs(
  page: Page,
  options: LoginOptions
): Promise<void> {
  const { email, role } = options;

  console.log(`🔐 Validating authentication for ${email} with role ${role}`);

  // First, ensure the user has the correct role in the database
  if (role === 'admin' || role === 'staff') {
    await ensureUserRole(page, email, role);
  }

  // Navigate to dashboard to verify authentication
  await page.goto('/dashboard');
  await page.waitForTimeout(500);

  // Check if we're authenticated
  if (page.url().includes('sign-in')) {
    console.warn('⚠️ Not authenticated - global setup may have failed');
    throw new Error(
      `Authentication required for test. Please ensure user ${email} exists in Clerk and E2E_USER_PASSWORD is set.`
    );
  }

  console.log('✅ Authentication validated successfully');
}

/**
 * Ensure a user has the specified role in the database
 * This is needed because users are created with 'user' role by default
 */
async function ensureUserRole(
  page: Page,
  email: string,
  role: string
): Promise<void> {
  try {
    console.log(`🔧 Setting user role to '${role}' for ${email}`);

    // Navigate to a page where we can access the Convex client
    await page.goto('/dashboard');
    await page.waitForTimeout(1000);

    // Call the Convex mutation to set the user role
    const result = await page.evaluate(
      async ({ email, role }) => {
        // Access the global Convex client that should be available on the page
        const convex = (window as any).convex;
        if (!convex) {
          throw new Error('Convex client not available on page');
        }

        // Call the mutation
        return await convex.mutation(
          'functions/testIntegration:setTestUserRole',
          {
            email,
            role,
          }
        );
      },
      { email, role }
    );

    console.log(`✅ User role set successfully:`, result);
  } catch (error) {
    console.warn(`⚠️ Failed to set user role: ${error}`);
    // Don't throw - let the test continue and fail later if role is actually needed
  }
}

/**
 * Check if user has admin/staff access by trying to access admin page
 */
export async function requireAdminAccess(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForTimeout(500);

  if (page.url().includes('sign-in') || page.url().includes('dashboard')) {
    throw new Error(
      'Admin access required for this test. User may not have admin/staff role.'
    );
  }

  console.log('✅ Admin access confirmed');
}

/**
 * Navigate to admin activities tab (DRY helper for activity tests)
 */
export async function navigateToActivitiesTab(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForLoadState('domcontentloaded');

  // Wait for and click the Activities button (it's a button, not a tab)
  const activitiesButton = page
    .getByRole('button')
    .filter({ hasText: 'Activities' });
  await activitiesButton.waitFor({ state: 'visible', timeout: 5000 });
  await activitiesButton.click();

  // Wait for the activities management interface to load
  await expect(page.getByText('Manage Activities')).toBeVisible({
    timeout: 5000,
  });

  console.log('✅ Navigated to Activities management tab');
}

/**
 * Navigate to admin rewards tab (DRY helper for rewards tests)
 */
export async function navigateToRewardsTab(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForLoadState('domcontentloaded');

  // Wait for the page to load and check if we're already on the rewards tab
  try {
    await expect(page.getByText('Manage Rewards')).toBeVisible({
      timeout: 3000,
    });
    console.log('✅ Already on Rewards management tab');
    return;
  } catch {
    // Not on rewards tab, need to click it
  }

  const rewardsButton = page.getByRole('button').filter({ hasText: 'Rewards' });
  await rewardsButton.waitFor({ state: 'visible', timeout: 5000 });
  await rewardsButton.click();

  await expect(page.getByText('Manage Rewards')).toBeVisible({
    timeout: 10000,
  });

  console.log('✅ Navigated to Rewards management tab');
}

/**
 * Navigate to admin milestones tab (DRY helper for milestone tests)
 */
export async function navigateToMilestonesTab(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForLoadState('domcontentloaded');

  const milestonesButton = page
    .getByRole('button')
    .filter({ hasText: 'Milestones' });
  await milestonesButton.waitFor({ state: 'visible', timeout: 5000 });
  await milestonesButton.click();

  await expect(page.getByText('Manage Milestones')).toBeVisible({
    timeout: 5000,
  });

  console.log('✅ Navigated to Milestones management tab');
}

/**
 * Helper to logout the current user
 */
export async function logout(page: Page): Promise<void> {
  await page.evaluate(() => {
    localStorage.clear();
    sessionStorage.clear();
  });

  await page.goto('/');
  await page.waitForTimeout(500);
}

/**
 * Helper to check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check for common authenticated elements
    const authElements = [
      '[data-testid="authenticated-content"]',
      '[data-testid="user-menu"]',
      '[data-testid="dashboard"]',
      'text=Dashboard',
      'text=Admin',
      'text=Log Activity',
    ];

    for (const selector of authElements) {
      try {
        await page.waitForSelector(selector, { timeout: 1000 });
        return true;
      } catch {
        continue;
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}
