import { test, expect } from '@playwright/test';
import { loginAs, isAuthenticated } from './utils/auth';

test.describe('Debug App State', () => {
  test('should show what the app looks like on startup', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Listen for page errors
    const pageErrors: string[] = [];
    page.on('pageerror', (error) => {
      pageErrors.push(error.message);
    });

    // Navigate to the app
    await page.goto('/');

    // Wait for page to load
    await page.waitForLoadState('domcontentloaded');

    // Wait a bit more for React to potentially render
    await page.waitForTimeout(2000);

    // Take a screenshot to see what we're dealing with
    await page.screenshot({ path: 'debug-app-startup.png', fullPage: true });

    // Log the current URL
    console.log('Current URL:', page.url());

    // Log the page title
    const title = await page.title();
    console.log('Page title:', title);

    // Log any errors
    if (consoleErrors.length > 0) {
      console.log('Console errors:', consoleErrors);
    }
    if (pageErrors.length > 0) {
      console.log('Page errors:', pageErrors);
    }

    // Check if React root element exists
    const reactRoot = await page
      .locator('#root')
      .isVisible()
      .catch(() => false);
    console.log('React root element exists:', reactRoot);

    // Check if any React components loaded
    const reactElements = await page
      .locator('[data-reactroot], [data-react-helmet]')
      .count();
    console.log('React elements found:', reactElements);

    // Check if we can find any common elements
    const commonSelectors = [
      'text=Sign in',
      'text=Login',
      'text=Dashboard',
      'text=Admin',
      'text=Activities',
      '[data-testid="authenticated-content"]',
      'button',
      'nav',
      'main',
    ];

    for (const selector of commonSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`${selector}: ${isVisible ? 'FOUND' : 'NOT FOUND'}`);
      } catch (error) {
        console.log(`${selector}: ERROR - ${error.message}`);
      }
    }

    // Get the page content for debugging
    const bodyText = await page.locator('body').textContent();
    console.log('Page content preview:', bodyText?.substring(0, 500));
  });

  test('should test authentication flow', async ({ page }) => {
    // Try the login function
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });

    // Take a screenshot after login attempt
    await page.screenshot({ path: 'debug-after-login.png', fullPage: true });

    // Check if we're authenticated
    const authenticated = await isAuthenticated(page);
    console.log('Authentication status:', authenticated);

    // Log current URL after login attempt
    console.log('URL after login:', page.url());

    // Try to navigate to admin
    await page.goto('/admin');
    await page.screenshot({ path: 'debug-admin-page.png', fullPage: true });

    console.log('URL after admin navigation:', page.url());

    // Look for admin-specific elements
    const adminSelectors = [
      'text=Activities',
      'text=Manage Activities',
      '[role="tab"]',
      'button[role="tab"]',
      'text=Admin',
      'text=Dashboard',
    ];

    for (const selector of adminSelectors) {
      try {
        const element = await page.locator(selector).first();
        const isVisible = await element.isVisible();
        console.log(`Admin ${selector}: ${isVisible ? 'FOUND' : 'NOT FOUND'}`);

        if (isVisible) {
          const text = await element.textContent();
          console.log(`  Text content: "${text}"`);
        }
      } catch (error) {
        console.log(`Admin ${selector}: ERROR - ${error.message}`);
      }
    }
  });

  test('should explore available routes', async ({ page }) => {
    const routes = ['/', '/dashboard', '/admin', '/login', '/sign-in'];

    for (const route of routes) {
      try {
        await page.goto(route);
        await page.waitForLoadState('domcontentloaded');

        const title = await page.title();
        const url = page.url();

        console.log(`Route ${route}:`);
        console.log(`  Final URL: ${url}`);
        console.log(`  Title: ${title}`);

        // Take a screenshot
        await page.screenshot({
          path: `debug-route-${route.replace('/', 'root')}.png`,
          fullPage: true,
        });

        // Check for key elements
        const hasButton = await page
          .locator('button')
          .first()
          .isVisible()
          .catch(() => false);
        const hasNav = await page
          .locator('nav')
          .first()
          .isVisible()
          .catch(() => false);
        const hasMain = await page
          .locator('main')
          .first()
          .isVisible()
          .catch(() => false);

        console.log(`  Has button: ${hasButton}`);
        console.log(`  Has nav: ${hasNav}`);
        console.log(`  Has main: ${hasMain}`);
        console.log('---');
      } catch (error) {
        console.log(`Route ${route}: ERROR - ${error.message}`);
      }
    }
  });
});
