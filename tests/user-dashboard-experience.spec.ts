import { test, expect } from '@playwright/test';
import {
  loginAs,
  requireAdminAccess,
  navigateToActivitiesTab,
  navigateToMilestonesTab,
} from './utils/auth';
import {
  seedTestActivities,
  cleanupTestActivities,
  seedTestMilestones,
  cleanupTestMilestones,
  TEST_ACTIVITIES,
  TEST_MILESTONES,
} from './utils/test-data';

test.describe('DASHBOARD-1: Member Dashboard Display', () => {
  test.beforeEach(async ({ page }) => {
    // Seed data as admin
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await requireAdminAccess(page);
    await navigateToActivitiesTab(page);
    await seedTestActivities(page);
    await navigateToMilestonesTab(page);
    await seedTestMilestones(page);

    // Navigate to user-facing dashboard
    await page.goto('/dashboard');
    await page.waitForLoadState('domcontentloaded');
  });

  test.afterEach(async ({ page }) => {
    // Cleanup as admin
    await navigateToMilestonesTab(page);
    await cleanupTestMilestones(page);
    await navigateToActivitiesTab(page);
    await cleanupTestActivities(page);
  });

  test('should display points balance and tier status', async ({ page }) => {
    await expect(page.getByTestId('user-points-balance')).toBeVisible();
    await expect(page.getByTestId('user-tier-status')).toBeVisible();
  });

  test('should display recent activity timeline', async ({ page }) => {
    await expect(
      page.getByRole('heading', { name: 'Recent Activity' })
    ).toBeVisible();
    // Further tests would log an activity and check if it appears here.
  });

  test('should display milestone progress', async ({ page }) => {
    await expect(
      page.getByRole('heading', { name: 'My Milestones' })
    ).toBeVisible();
    await expect(
      page.getByText(TEST_MILESTONES.TEN_CLASSES.name)
    ).toBeVisible();
  });
});

test.describe('DASHBOARD-2: Activity Logging Experience', () => {
  test.beforeEach(async ({ page }) => {
    await loginAs(page, { email: '<EMAIL>', role: 'member' });
    await page.goto('/dashboard');
  });

  test('should log an activity and receive points', async ({ page }) => {
    const pointsBalance = page.getByTestId('user-points-balance');
    const initialPoints = parseInt(
      (await pointsBalance.textContent()) || '0',
      10
    );

    // Log an activity
    await page.getByRole('button', { name: 'Log Activity' }).click();
    await page
      .getByRole('button', { name: TEST_ACTIVITIES.BASIC.name })
      .click();

    // Verify confirmation
    await expect(page.getByText('Activity logged successfully!')).toBeVisible();

    // Verify points update
    const newPoints = parseInt((await pointsBalance.textContent()) || '0', 10);
    expect(newPoints).toBeGreaterThan(initialPoints);
  });
});
