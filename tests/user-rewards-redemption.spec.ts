import { test, expect } from '@playwright/test';
import {
  loginAs,
  requireAdminAccess,
  navigateToRewardsTab,
} from './utils/auth';
import {
  seedTestRewards,
  cleanupTestRewards,
  TEST_REWARDS,
} from './utils/test-data';

test.describe('REWARDS-1 & 2: Catalog Display and Redemption Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Seed data as admin
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await requireAdminAccess(page);
    await navigateToRewardsTab(page);
    await seedTestRewards(page);

    // Navigate to user-facing rewards page
    await page.goto('/rewards');
    await page.waitForLoadState('domcontentloaded');
  });

  test.afterEach(async ({ page }) => {
    // Cleanup as admin
    await navigateToRewardsTab(page);
    await cleanupTestRewards(page);
  });

  test('should display rewards catalog with correct states', async ({
    page,
  }) => {
    // Check for point balance
    await expect(page.getByTestId('user-points-balance')).toBeVisible();

    // Check for visible reward
    const affordableReward = page.getByTestId(
      `reward-card-${TEST_REWARDS.BASIC.name}`
    );
    await expect(affordableReward).toBeVisible();
    await expect(affordableReward).not.toHaveClass(/unavailable/);

    // Check for unaffordable reward (assuming user has less than HIGH_COST)
    const unaffordableReward = page.getByTestId(
      `reward-card-${TEST_REWARDS.HIGH_COST.name}`
    );
    await expect(unaffordableReward).toBeVisible();
    await expect(unaffordableReward).toHaveClass(/unavailable/);
  });

  test('should open reward detail modal', async ({ page }) => {
    await page.getByTestId(`reward-card-${TEST_REWARDS.BASIC.name}`).click();
    await expect(
      page.getByRole('heading', { name: TEST_REWARDS.BASIC.name })
    ).toBeVisible();
    await expect(page.getByText(TEST_REWARDS.BASIC.description)).toBeVisible();
    await expect(page.getByRole('button', { name: 'Redeem' })).toBeEnabled();
  });

  test('should redeem a reward successfully', async ({ page }) => {
    // Get initial points
    const pointsBalance = page.getByTestId('user-points-balance');
    const initialPoints = parseInt(
      (await pointsBalance.textContent()) || '0',
      10
    );

    // Redeem reward
    await page.getByTestId(`reward-card-${TEST_REWARDS.BASIC.name}`).click();
    await page.getByRole('button', { name: 'Redeem' }).click();

    // Confirmation dialog
    await page.getByRole('button', { name: 'Confirm' }).click();

    // Verify success
    await expect(page.getByText('Reward redeemed successfully!')).toBeVisible();

    // Verify points deducted
    const newPoints = parseInt((await pointsBalance.textContent()) || '0', 10);
    expect(newPoints).toBe(initialPoints - TEST_REWARDS.BASIC.cost);

    // Verify redemption history
    await page.goto('/history/redemptions');
    await expect(page.getByText(TEST_REWARDS.BASIC.name)).toBeVisible();
  });

  test('should handle insufficient points error', async ({ page }) => {
    // Click on unaffordable reward
    await page
      .getByTestId(`reward-card-${TEST_REWARDS.HIGH_COST.name}`)
      .click();

    // Check that redeem button is disabled
    await expect(page.getByRole('button', { name: 'Redeem' })).toBeDisabled();
  });
});
