import { test, expect } from '@playwright/test';
import { loginAs } from './utils/auth';

// For now, these are simplified. A real implementation would need more robust user creation.
async function createUserAndSetPoints(page: any, name: string, points: number) {
  // This is a placeholder for what would be a complex user creation flow.
  // In a real scenario, this would involve UI signup or a test-only API.
  // For this test, we'll assume users exist and we can navigate as them.
  // We will test the UI with the main user.
  console.log(`Setting up user ${name} with ${points} points.`);
}

test.describe('LEADERBOARD-1: Privacy-First Design', () => {
  test.beforeEach(async ({ page }) => {
    // For this test, we'll use the main authenticated user.
    await loginAs(page, { email: '<EMAIL>', role: 'member' });
    await page.goto('/leaderboard');
    await page.waitForLoadState('domcontentloaded');
  });

  test('should display leaderboard with anonymous users by default', async ({
    page,
  }) => {
    await expect(
      page.getByRole('heading', { name: 'Leaderboard' })
    ).toBeVisible();

    // Check for anonymous names and avatars
    const anonymousUser = page.getByTestId('leaderboard-user-row').first();
    await expect(anonymousUser.getByTestId('user-avatar')).toBeVisible();

    // Anonymous names are like "Golden Warrior". They don't contain '@'.
    const userName = await anonymousUser.getByTestId('user-name').textContent();
    expect(userName).not.toContain('@');
    expect(userName).not.toContain('willbash');
  });

  test('should allow user to reveal their real name', async ({ page }) => {
    // Go to settings to change privacy
    await page.goto('/settings');
    const privacyToggle = page.getByRole('switch', {
      name: 'Show my real name on leaderboards',
    });
    await privacyToggle.check();
    await expect(page.getByText('Privacy setting updated!')).toBeVisible();

    // Go back to leaderboard
    await page.goto('/leaderboard');
    const userRow = page.getByTestId('leaderboard-user-row').first();
    const userName = await userRow.getByTestId('user-name').textContent();
    expect(userName?.toLowerCase()).toContain('will'); // or part of the real name
  });
});

test.describe('LEADERBOARD-2: Ranking & Competition', () => {
  // These tests would require a more robust setup with multiple users and varied points.
  // Skipping for now as the user creation part is complex.
  test.skip('should display users ranked by points', async ({ page }) => {
    // 1. Create multiple users with different points.
    // 2. Go to leaderboard.
    // 3. Assert that the users appear in the correct order.
  });

  test.skip('should highlight the current user in the list', async ({
    page,
  }) => {
    // 1. Ensure current user is on the leaderboard.
    // 2. Assert that the user's row has a special highlight class.
  });
});
