import { test, expect } from '@playwright/test';
import {
  loginAs,
  requireAdminAccess,
  navigateToMilestonesTab,
} from './utils/auth';
import {
  createTestMilestone,
  cleanupTestMilestones,
  waitForMilestoneTable,
  TEST_MILESTONES,
  seedTestActivities,
  cleanupTestActivities,
  VALIDATION_MESSAGES,
} from './utils/test-data';

test.describe('MILESTONE-1: Milestone CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await requireAdminAccess(page);
    await navigateToMilestonesTab(page);
    await waitForMilestoneTable(page);
    // Milestones depend on activities, so we seed them.
    await seedTestActivities(page);
  });

  test.afterEach(async ({ page }) => {
    await cleanupTestMilestones(page);
    await cleanupTestActivities(page);
  });

  test('should display milestone management interface', async ({ page }) => {
    await expect(
      page.getByRole('heading', { name: 'Manage Milestones' })
    ).toBeVisible();
    await expect(
      page.getByText('Set up achievements for members to unlock')
    ).toBeVisible();
    await expect(
      page.getByRole('button', { name: 'Create Milestone' })
    ).toBeVisible();
  });

  test('should create a new milestone successfully', async ({ page }) => {
    await createTestMilestone(page, TEST_MILESTONES.FIVE_YOGA);

    await expect(
      page.getByRole('cell', { name: TEST_MILESTONES.FIVE_YOGA.name })
    ).toBeVisible();
    await expect(
      page.getByText(
        `Attend ${TEST_MILESTONES.FIVE_YOGA.countThreshold} ${TEST_MILESTONES.FIVE_YOGA.activityMatcher} classes`
      )
    ).toBeVisible();
  });

  test('should edit an existing milestone', async ({ page }) => {
    await createTestMilestone(page, TEST_MILESTONES.TEN_CLASSES);

    await page
      .getByRole('row', { name: new RegExp(TEST_MILESTONES.TEN_CLASSES.name) })
      .getByRole('button', { name: 'Edit' })
      .click();

    await expect(
      page.getByRole('heading', { name: 'Edit Milestone' })
    ).toBeVisible();

    await page.getByLabel('Name').fill('Updated Test Milestone');
    await page.getByLabel('Count Threshold').fill('15');

    await page.getByRole('button', { name: 'Save' }).click();

    await expect(
      page.getByText('Milestone successfully updated!')
    ).toBeVisible();
    await expect(
      page.getByRole('cell', { name: 'Updated Test Milestone' })
    ).toBeVisible();
  });

  test('should delete a milestone', async ({ page }) => {
    await createTestMilestone(page, TEST_MILESTONES.TEN_CLASSES);

    await deleteTestMilestone(page, TEST_MILESTONES.TEN_CLASSES.name);

    await expect(
      page.getByRole('cell', { name: TEST_MILESTONES.TEN_CLASSES.name })
    ).not.toBeVisible();
  });

  test('should enable and disable a milestone', async ({ page }) => {
    await createTestMilestone(page, TEST_MILESTONES.TEN_CLASSES);

    const row = page.getByRole('row', {
      name: new RegExp(TEST_MILESTONES.TEN_CLASSES.name),
    });
    const activeToggle = row.getByRole('switch');

    await activeToggle.uncheck();
    await expect(page.getByText('Milestone status updated!')).toBeVisible();

    await activeToggle.check();
    await expect(page.getByText('Milestone status updated!')).toBeVisible();
  });
});

test.describe('MILESTONE-2: Milestone Logic Validation', () => {
  test.beforeEach(async ({ page }) => {
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await requireAdminAccess(page);
    await navigateToMilestonesTab(page);
    await waitForMilestoneTable(page);
  });

  test('should validate required fields for milestone creation', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Create Milestone' }).click();
    await page.getByRole('button', { name: 'Save' }).click();

    await expect(
      page.getByText(VALIDATION_MESSAGES.NAME_TOO_SHORT)
    ).toBeVisible();
    await expect(
      page.getByText('Count must be a positive number')
    ).toBeVisible();
    await expect(
      page.getByText('Reward must be a positive number')
    ).toBeVisible();
  });
});
