{"cookies": [{"name": "__clerk_db_jwt_zXj5PpbZ", "value": "dvb_2yaPwTJVZjeosvTn0dEgbJokMFK", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "__clerk_db_jwt", "value": "dvb_2yaPwTJVZjeosvTn0dEgbJokMFK", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "__cf_bm", "value": "IN31CLmIn_8IxUuVPx4hskBL9_xFHUxLFLaOKpbiPPI-**********-*******-QYLm_8jEKdDpizXC7CGydj7vNqxz1nR4uNrPB84ST71CoOrww2bIcx7EWObngWaCyZxbT.qOel1jXhZpcaiPwjzxH35TP7_QBvt.jCdnaoE", "domain": ".mutual-buffalo-71.clerk.accounts.dev", "path": "/", "expires": **********.753817, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_cfuvid", "value": "d9SPECADuKuKK_5L4KNcc62Hx3th43foOaDjkc2h7f0-*************-*******-*********", "domain": ".mutual-buffalo-71.clerk.accounts.dev", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "__session", "value": "eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3ZURUR3RzlrbllpNUI0dWV1bUJ1U2tKSmciLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CM_9nT64kwkAPnU2ZIekow4oHs1SZijOPm60IpnQnY-S8JO0iNrjOVtDyd5zxDxsYjixzONrnpAnuBsvYZRzDPSKqAmDHm_sUXzDNqJYimzIHMGKqujtI-R16Nl0SFhO0gMHlshSo4CzsPBq6LO5SgeLKfqza8QkHsTCsncgvHS00zRlXNifYrtTQtpD9JGETqvqfTLtVOFZ_NGxrgm9rBBa8tbR1NaPMhU5867zMNvadsPNx_9H5SuGbnl9E6mAZYrjxm-82DBG5ZR6aELZE2koLZNCyEUlRJB1wXtl2tOBOoDoCqGfTQYdSMqM25gJTXtYPr5f5HCymX4CFlPgTA", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "__session_zXj5PpbZ", "value": "eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18yd3ZURUR3RzlrbllpNUI0dWV1bUJ1U2tKSmciLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CM_9nT64kwkAPnU2ZIekow4oHs1SZijOPm60IpnQnY-S8JO0iNrjOVtDyd5zxDxsYjixzONrnpAnuBsvYZRzDPSKqAmDHm_sUXzDNqJYimzIHMGKqujtI-R16Nl0SFhO0gMHlshSo4CzsPBq6LO5SgeLKfqza8QkHsTCsncgvHS00zRlXNifYrtTQtpD9JGETqvqfTLtVOFZ_NGxrgm9rBBa8tbR1NaPMhU5867zMNvadsPNx_9H5SuGbnl9E6mAZYrjxm-82DBG5ZR6aELZE2koLZNCyEUlRJB1wXtl2tOBOoDoCqGfTQYdSMqM25gJTXtYPr5f5HCymX4CFlPgTA", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "clerk_active_context", "value": "sess_2yaPwlZLDymVorPJ5c9WMfQfsnU:org_2y4e81Qmc2NefgZepsHnjAxBUhA", "domain": "localhost", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "__client_uat_zXj5PpbZ", "value": "1750069951", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Strict"}, {"name": "__client_uat", "value": "1750069951", "domain": "localhost", "path": "/", "expires": **********, "httpOnly": false, "secure": false, "sameSite": "Strict"}], "origins": [{"origin": "http://localhost:3000", "localStorage": [{"name": "__clerk_environment", "value": "{\"value\":{\"object\":\"environment\",\"auth_config\":{\"claimed_at\":null,\"id\":\"\",\"object\":\"auth_config\",\"reverification\":true,\"single_session_mode\":true},\"display_config\":{\"object\":\"display_config\",\"after_create_organization_url\":\"http://localhost:3000\",\"after_join_waitlist_url\":\"http://localhost:3000\",\"after_leave_organization_url\":\"http://localhost:3000\",\"after_sign_in_url\":\"http://localhost:3000\",\"after_sign_out_all_url\":\"http://localhost:3000/\",\"after_sign_out_one_url\":\"https://mutual-buffalo-71.accounts.dev/sign-in/choose\",\"after_sign_up_url\":\"http://localhost:3000\",\"after_switch_session_url\":\"http://localhost:3000\",\"application_name\":\"fitness-rewards-platform\",\"branded\":true,\"captcha_heartbeat\":false,\"captcha_oauth_bypass\":[],\"captcha_provider\":\"turnstile\",\"captcha_public_key_invisible\":\"0x4AAAAAAAFV93qQdS0ycilX\",\"captcha_public_key\":\"0x4AAAAAAAWXJGBD7bONzLBd\",\"captcha_widget_type\":\"smart\",\"clerk_js_version\":\"5\",\"create_organization_url\":\"https://mutual-buffalo-71.accounts.dev/create-organization\",\"favicon_image_url\":\"https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2ltYWdlcy5jbGVyay5kZXYvdXBsb2FkZWQvaW1nXzJ5NGYyc0Zrb3RJcjYzZG5rZU14NWp2OVpKUSJ9\",\"home_url\":\"http://localhost:3000\",\"id\":\"display_config_2wvTEChXVweu1fFW8Ow5iXrWVRu\",\"instance_environment_type\":\"development\",\"logo_image_url\":\"https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2ltYWdlcy5jbGVyay5kZXYvdXBsb2FkZWQvaW1nXzJ5NGYxQ0ZGMjA0Nmw5N0s5RHRYTjY4dmZ6QiJ9\",\"organization_profile_url\":\"https://mutual-buffalo-71.accounts.dev/organization\",\"preferred_sign_in_strategy\":\"password\",\"privacy_policy_url\":\"\",\"show_devmode_warning\":true,\"sign_in_url\":\"http://localhost:3000/sign-in\",\"sign_up_url\":\"http://localhost:3000/sign-up\",\"support_email\":\"\",\"terms_url\":\"\",\"theme\":{\"buttons\":{\"font_color\":\"#ffffff\",\"font_family\":\"\\\"Source Sans Pro\\\", sans-serif\",\"font_weight\":\"600\"},\"general\":{\"color\":\"#6c47ff\",\"padding\":\"1em\",\"box_shadow\":\"0 2px 8px rgba(0, 0, 0, 0.2)\",\"font_color\":\"#151515\",\"font_family\":\"\\\"Source Sans Pro\\\", sans-serif\",\"border_radius\":\"0.5em\",\"background_color\":\"#ffffff\",\"label_font_weight\":\"600\"},\"accounts\":{\"background_color\":\"#ffffff\"}},\"user_profile_url\":\"https://mutual-buffalo-71.accounts.dev/user\",\"waitlist_url\":\"https://mutual-buffalo-71.accounts.dev/waitlist\"},\"id\":\"\",\"maintenance_mode\":false,\"organization_settings\":{\"actions\":{\"admin_delete\":false},\"domains\":{\"enabled\":false,\"enrollment_modes\":[],\"default_role\":\"org:member\"},\"enabled\":true,\"max_allowed_memberships\":5},\"user_settings\":{\"actions\":{\"delete_self\":true,\"create_organization\":false,\"create_organizations_limit\":null},\"attributes\":{\"email_address\":{\"enabled\":true,\"required\":true,\"used_for_first_factor\":true,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[\"email_code\"],\"verify_at_sign_up\":true,\"name\":\"email_address\"},\"phone_number\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"phone_number\"},\"username\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"username\"},\"web3_wallet\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"web3_wallet\"},\"first_name\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"first_name\"},\"last_name\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"last_name\"},\"password\":{\"enabled\":true,\"required\":true,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"password\"},\"authenticator_app\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"authenticator_app\"},\"ticket\":{\"enabled\":true,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"ticket\"},\"backup_code\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"backup_code\"},\"passkey\":{\"enabled\":false,\"required\":false,\"used_for_first_factor\":false,\"first_factors\":[],\"used_for_second_factor\":false,\"second_factors\":[],\"verifications\":[],\"verify_at_sign_up\":false,\"name\":\"passkey\"}},\"passkey_settings\":{\"allow_autofill\":true,\"show_sign_in_button\":true},\"password_settings\":{\"disable_hibp\":false,\"min_length\":8,\"max_length\":72,\"require_special_char\":false,\"require_numbers\":false,\"require_uppercase\":false,\"require_lowercase\":false,\"show_zxcvbn\":false,\"min_zxcvbn_strength\":0,\"enforce_hibp_on_sign_in\":true,\"allowed_special_characters\":\"!\\\"#$%&'()*+,-./:;<=>?@[]^_`{|}~\"},\"saml\":{\"enabled\":false},\"sign_in\":{\"second_factor\":{\"required\":false}},\"sign_up\":{\"captcha_enabled\":true,\"captcha_widget_type\":\"smart\",\"custom_action_required\":false,\"progressive\":true,\"mode\":\"public\",\"legal_consent_enabled\":false},\"social\":{\"oauth_facebook\":{\"enabled\":false,\"required\":false,\"authenticatable\":false,\"block_email_subaddresses\":false,\"strategy\":\"oauth_facebook\",\"not_selectable\":false,\"deprecated\":false,\"name\":\"Facebook\",\"logo_url\":\"https://img.clerk.com/static/facebook.png\"},\"oauth_google\":{\"enabled\":true,\"required\":false,\"authenticatable\":true,\"block_email_subaddresses\":true,\"strategy\":\"oauth_google\",\"not_selectable\":false,\"deprecated\":false,\"name\":\"Google\",\"logo_url\":\"https://img.clerk.com/static/google.png\"}}},\"commerce_settings\":{\"billing\":{\"stripe_publishable_key\":\"\",\"enabled\":false,\"has_paid_user_plans\":false,\"has_paid_org_plans\":false}},\"api_keys_settings\":{\"enabled\":false}},\"exp\":1750156351533}"}]}]}